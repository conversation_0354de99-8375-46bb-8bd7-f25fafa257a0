<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_datagram_socket::cancel</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_datagram_socket.html" title="basic_datagram_socket">
<link rel="prev" href="bytes_readable.html" title="basic_datagram_socket::bytes_readable">
<link rel="next" href="cancel/overload1.html" title="basic_datagram_socket::cancel (1 of 2 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="bytes_readable.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="cancel/overload1.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_datagram_socket.cancel"></a><a class="link" href="cancel.html" title="basic_datagram_socket::cancel">basic_datagram_socket::cancel</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.basic_datagram_socket.cancel"></a> 
Cancel
          all asynchronous operations associated with the socket.
        </p>
<pre class="programlisting">void <a class="link" href="cancel/overload1.html" title="basic_datagram_socket::cancel (1 of 2 overloads)">cancel</a>();
  <span class="emphasis"><em>» <a class="link" href="cancel/overload1.html" title="basic_datagram_socket::cancel (1 of 2 overloads)">more...</a></em></span>

void <a class="link" href="cancel/overload2.html" title="basic_datagram_socket::cancel (2 of 2 overloads)">cancel</a>(
    asio::error_code &amp; ec);
  <span class="emphasis"><em>» <a class="link" href="cancel/overload2.html" title="basic_datagram_socket::cancel (2 of 2 overloads)">more...</a></em></span>
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="bytes_readable.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="cancel/overload1.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
