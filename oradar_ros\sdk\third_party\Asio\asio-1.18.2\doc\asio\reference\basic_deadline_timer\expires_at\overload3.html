<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_deadline_timer::expires_at (3 of 3 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../expires_at.html" title="basic_deadline_timer::expires_at">
<link rel="prev" href="overload2.html" title="basic_deadline_timer::expires_at (2 of 3 overloads)">
<link rel="next" href="../expires_from_now.html" title="basic_deadline_timer::expires_from_now">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload2.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../expires_at.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="../expires_from_now.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_deadline_timer.expires_at.overload3"></a><a class="link" href="overload3.html" title="basic_deadline_timer::expires_at (3 of 3 overloads)">basic_deadline_timer::expires_at
          (3 of 3 overloads)</a>
</h5></div></div></div>
<p>
            Set the timer's expiry time as an absolute time.
          </p>
<pre class="programlisting">std::size_t expires_at(
    const time_type &amp; expiry_time,
    asio::error_code &amp; ec);
</pre>
<p>
            This function sets the expiry time. Any pending asynchronous wait operations
            will be cancelled. The handler for each cancelled operation will be invoked
            with the <code class="computeroutput">asio::error::operation_aborted</code> error code.
          </p>
<h6>
<a name="asio.reference.basic_deadline_timer.expires_at.overload3.h0"></a>
            <span><a name="asio.reference.basic_deadline_timer.expires_at.overload3.parameters"></a></span><a class="link" href="overload3.html#asio.reference.basic_deadline_timer.expires_at.overload3.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">expiry_time</span></dt>
<dd><p>
                  The expiry time to be used for the timer.
                </p></dd>
<dt><span class="term">ec</span></dt>
<dd><p>
                  Set to indicate what error occurred, if any.
                </p></dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_deadline_timer.expires_at.overload3.h1"></a>
            <span><a name="asio.reference.basic_deadline_timer.expires_at.overload3.return_value"></a></span><a class="link" href="overload3.html#asio.reference.basic_deadline_timer.expires_at.overload3.return_value">Return
            Value</a>
          </h6>
<p>
            The number of asynchronous operations that were cancelled.
          </p>
<h6>
<a name="asio.reference.basic_deadline_timer.expires_at.overload3.h2"></a>
            <span><a name="asio.reference.basic_deadline_timer.expires_at.overload3.remarks"></a></span><a class="link" href="overload3.html#asio.reference.basic_deadline_timer.expires_at.overload3.remarks">Remarks</a>
          </h6>
<p>
            If the timer has already expired when <code class="computeroutput">expires_at()</code> is called,
            then the handlers for asynchronous wait operations will:
          </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
                have already been invoked; or
              </li>
<li class="listitem">
                have been queued for invocation in the near future.
              </li>
</ul></div>
<p>
            These handlers can no longer be cancelled, and therefore are passed an
            error code that indicates the successful completion of the wait operation.
          </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload2.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../expires_at.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="../expires_from_now.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
