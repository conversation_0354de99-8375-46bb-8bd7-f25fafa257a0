<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_datagram_socket::set_option</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_datagram_socket.html" title="basic_datagram_socket">
<link rel="prev" href="send_to/overload3.html" title="basic_datagram_socket::send_to (3 of 3 overloads)">
<link rel="next" href="set_option/overload1.html" title="basic_datagram_socket::set_option (1 of 2 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="send_to/overload3.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="set_option/overload1.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_datagram_socket.set_option"></a><a class="link" href="set_option.html" title="basic_datagram_socket::set_option">basic_datagram_socket::set_option</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.basic_datagram_socket.set_option"></a> 
Set
          an option on the socket.
        </p>
<pre class="programlisting">template&lt;
    typename <a class="link" href="../SettableSocketOption.html" title="Settable socket option requirements">SettableSocketOption</a>&gt;
void <a class="link" href="set_option/overload1.html" title="basic_datagram_socket::set_option (1 of 2 overloads)">set_option</a>(
    const SettableSocketOption &amp; option);
  <span class="emphasis"><em>» <a class="link" href="set_option/overload1.html" title="basic_datagram_socket::set_option (1 of 2 overloads)">more...</a></em></span>

template&lt;
    typename <a class="link" href="../SettableSocketOption.html" title="Settable socket option requirements">SettableSocketOption</a>&gt;
void <a class="link" href="set_option/overload2.html" title="basic_datagram_socket::set_option (2 of 2 overloads)">set_option</a>(
    const SettableSocketOption &amp; option,
    asio::error_code &amp; ec);
  <span class="emphasis"><em>» <a class="link" href="set_option/overload2.html" title="basic_datagram_socket::set_option (2 of 2 overloads)">more...</a></em></span>
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="send_to/overload3.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="set_option/overload1.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
