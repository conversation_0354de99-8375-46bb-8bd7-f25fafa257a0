# Copyright 2018 The Cartographer Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Top-level proto and C++ targets for Cartographer.

load("@com_github_antonovvk_bazel_rules//:config.bzl", "cc_fix_config")

package(
    default_visibility = ["//visibility:public"],
)

licenses(["notice"])  # Apache 2.0

proto_library(
    name = "protos",
    srcs = glob([
        "**/*.proto",
    ]),
    deps = [
        "@com_google_protobuf//:empty_proto",
    ],
)

cc_proto_library(
    name = "cc_protos",
    deps = [":protos"],
)

cc_fix_config(
    name = "common_config_h",
    cmake = True,
    files = {"common/config.h.cmake": "common/config.h"},
    values = {
        "CARTOGRAPHER_CONFIGURATION_FILES_DIRECTORY": "configuration_files",
        "PROJECT_SOURCE_DIR": "todo_set_project_source_dir_in_cartographer.BUILD",
    },
    visibility = ["//visibility:private"],
)

TEST_LIBRARY_SRCS = glob([
    "**/*test_helpers.cc",
    "**/fake_*.cc",
    "**/mock_*.cc",
])

TEST_LIBRARY_HDRS = glob([
    "**/*test_helpers.h",
    "**/fake_*.h",
    "**/mock_*.h",
])

cc_library(
    name = "cartographer_test_library",
    testonly = 1,
    srcs = TEST_LIBRARY_SRCS,
    hdrs = TEST_LIBRARY_HDRS,
    deps = [
        ":cartographer",
        "@com_google_googletest//:gtest",
    ],
)

cc_library(
    name = "cartographer",
    srcs = glob(
        [
            "**/*.cc",
        ],
        exclude = [
            "**/*_main.cc",
            "**/*_test.cc",
        ] + TEST_LIBRARY_SRCS,
    ),
    hdrs = [
        "common/config.h",
    ] + glob(
        [
            "**/*.h",
        ],
        exclude = TEST_LIBRARY_HDRS,
    ),
    copts = ["-Wno-sign-compare"],
    includes = ["."],
    deps = [
        ":cc_protos",
        "@boost//:iostreams",
        "@com_google_absl//absl/base",
        "@com_google_absl//absl/container:flat_hash_map",
        "@com_google_absl//absl/container:flat_hash_set",
        "@com_google_absl//absl/strings",
        "@com_google_absl//absl/strings:str_format",
        "@com_google_absl//absl/synchronization",
        "@com_google_absl//absl/types:optional",
        "@com_google_glog//:glog",
        "@org_cairographics_cairo//:cairo",
        "@org_ceres_solver_ceres_solver//:ceres",
        "@org_lua_lua//:lua",
        "@org_tuxfamily_eigen//:eigen",
    ],
)

cc_binary(
    name = "cartographer_print_configuration",
    srcs = ["common/print_configuration_main.cc"],
    deps = [
        ":cartographer",
        "@com_github_gflags_gflags//:gflags",
        "@com_google_glog//:glog",
    ],
)

[cc_test(
    name = src.replace("/", "_").replace(".cc", ""),
    srcs = [src],
    data = ["//:configuration_files"],
    deps = [
        ":cartographer",
        ":cartographer_test_library",
        "@com_google_absl//absl/memory",
        "@com_google_googletest//:gtest_main",
    ],
) for src in glob(
    ["**/*_test.cc"],
)]
