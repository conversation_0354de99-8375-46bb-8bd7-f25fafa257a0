// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cartographer.sensor.proto;

message AdaptiveVoxelFilterOptions {
  // 'max_length' of a voxel edge.
  float max_length = 1;

  // If there are more points and not at least 'min_num_points' remain, the
  // voxel length is reduced trying to get this minimum number of points.
  float min_num_points = 2;

  // Points further away from the origin are removed.
  float max_range = 3;
}
