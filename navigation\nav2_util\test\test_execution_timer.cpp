// Copyright (c) 2018 Intel Corporation
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <chrono>
#include <thread>

#include "nav2_util/execution_timer.hpp"
#include "gtest/gtest.h"

using nav2_util::ExecutionTimer;
using std::this_thread::sleep_for;
using namespace std::chrono_literals;

TEST(ExecutionTimer, BasicDelay)
{
  ExecutionTimer t;
  t.start();
  sleep_for(10ns);
  t.end();
  ASSERT_GE(t.elapsed_time(), 10ns);
  ASSERT_GE(t.elapsed_time_in_seconds(), 1e-8);
}
