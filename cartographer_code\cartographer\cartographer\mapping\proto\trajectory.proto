// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cartographer.mapping.proto;

import "cartographer/transform/proto/transform.proto";

option java_outer_classname = "TrajectoryOuterClass";

message Trajectory {
  // NEXT_ID: 8
  message Node {
    // Index of this node within its trajectory.
    int32 node_index = 7;

    int64 timestamp = 1;

    // Transform from tracking to global map frame.
    transform.proto.Rigid3d pose = 5;
  }

  message Submap {
    // Index of this submap within its trajectory.
    int32 submap_index = 2;

    // Transform from submap to global map frame.
    transform.proto.Rigid3d pose = 1;
  }

  // ID of this trajectory.
  int32 trajectory_id = 3;

  // Time-ordered sequence of Nodes.
  repeated Node node = 1;

  // Submaps associated with the trajectory.
  repeated Submap submap = 2;
}
