// Copyright 2018 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cartographer.mapping.proto;

import "cartographer/mapping/proto/normal_estimation_options_2d.proto";

message TSDFRangeDataInserterOptions2D {
  // Distance to the surface within the signed distance function is evaluated.
  double truncation_distance = 1;
  // Maximum weight that can be stored in a cell.
  double maximum_weight = 2;

  // Enables updating cells between the sensor origin and the range observation
  // as free space.
  bool update_free_space = 3;

  NormalEstimationOptions2D normal_estimation_options = 4;

  // Project the distance between the updated cell und the range observation to
  // the estimated scan normal.
  bool project_sdf_distance_to_scan_normal = 5;

  // Update weight is scaled with 1/distance(origin,hit)^range_exponent.
  int32 update_weight_range_exponent = 6;

  // Kernel bandwidth of the weight factor based on the angle between.
  // scan normal and ray
  double update_weight_angle_scan_normal_to_ray_kernel_bandwidth = 7;

  // Kernel bandwidth of the weight factor based on the distance between
  // cell and scan observation.
  double update_weight_distance_cell_to_hit_kernel_bandwidth = 8;
}
