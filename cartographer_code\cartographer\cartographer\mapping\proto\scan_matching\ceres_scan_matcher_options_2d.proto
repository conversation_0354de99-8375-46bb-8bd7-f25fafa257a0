// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cartographer.mapping.scan_matching.proto;

import "cartographer/common/proto/ceres_solver_options.proto";

// NEXT ID: 10
message CeresScanMatcherOptions2D {
  // Scaling parameters for each cost functor.
  double occupied_space_weight = 1;
  double translation_weight = 2;
  double rotation_weight = 3;

  // Configure the Ceres solver. See the Ceres documentation for more
  // information: https://code.google.com/p/ceres-solver/
  common.proto.CeresSolverOptions ceres_solver_options = 9;
}
