<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_datagram_socket::enable_connection_aborted</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_datagram_socket.html" title="basic_datagram_socket">
<link rel="prev" href="do_not_route.html" title="basic_datagram_socket::do_not_route">
<link rel="next" href="endpoint_type.html" title="basic_datagram_socket::endpoint_type">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="do_not_route.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="endpoint_type.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_datagram_socket.enable_connection_aborted"></a><a class="link" href="enable_connection_aborted.html" title="basic_datagram_socket::enable_connection_aborted">basic_datagram_socket::enable_connection_aborted</a>
</h4></div></div></div>
<p>
          <span class="emphasis"><em>Inherited from socket_base.</em></span>
        </p>
<p>
          <a class="indexterm" name="asio.indexterm.basic_datagram_socket.enable_connection_aborted"></a> 
Socket
          option to report aborted connections on accept.
        </p>
<pre class="programlisting">typedef implementation_defined enable_connection_aborted;
</pre>
<p>
          Implements a custom socket option that determines whether or not an accept
          operation is permitted to fail with <code class="computeroutput">asio::error::connection_aborted</code>.
          By default the option is false.
        </p>
<h6>
<a name="asio.reference.basic_datagram_socket.enable_connection_aborted.h0"></a>
          <span><a name="asio.reference.basic_datagram_socket.enable_connection_aborted.examples"></a></span><a class="link" href="enable_connection_aborted.html#asio.reference.basic_datagram_socket.enable_connection_aborted.examples">Examples</a>
        </h6>
<p>
          Setting the option:
        </p>
<pre class="programlisting">asio::ip::tcp::acceptor acceptor(my_context);
...
asio::socket_base::enable_connection_aborted option(true);
acceptor.set_option(option);
</pre>
<p>
          Getting the current option value:
        </p>
<pre class="programlisting">asio::ip::tcp::acceptor acceptor(my_context);
...
asio::socket_base::enable_connection_aborted option;
acceptor.get_option(option);
bool is_set = option.value();
</pre>
<h6>
<a name="asio.reference.basic_datagram_socket.enable_connection_aborted.h1"></a>
          <span><a name="asio.reference.basic_datagram_socket.enable_connection_aborted.requirements"></a></span><a class="link" href="enable_connection_aborted.html#asio.reference.basic_datagram_socket.enable_connection_aborted.requirements">Requirements</a>
        </h6>
<p>
          <span class="emphasis"><em>Header: </em></span><code class="literal">asio/basic_datagram_socket.hpp</code>
        </p>
<p>
          <span class="emphasis"><em>Convenience header: </em></span><code class="literal">asio.hpp</code>
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="do_not_route.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="endpoint_type.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
