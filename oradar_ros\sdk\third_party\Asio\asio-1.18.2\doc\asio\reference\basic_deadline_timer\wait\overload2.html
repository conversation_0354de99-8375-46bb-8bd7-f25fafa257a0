<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_deadline_timer::wait (2 of 2 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../wait.html" title="basic_deadline_timer::wait">
<link rel="prev" href="overload1.html" title="basic_deadline_timer::wait (1 of 2 overloads)">
<link rel="next" href="../_basic_deadline_timer.html" title="basic_deadline_timer::~basic_deadline_timer">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../wait.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="../_basic_deadline_timer.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_deadline_timer.wait.overload2"></a><a class="link" href="overload2.html" title="basic_deadline_timer::wait (2 of 2 overloads)">basic_deadline_timer::wait
          (2 of 2 overloads)</a>
</h5></div></div></div>
<p>
            Perform a blocking wait on the timer.
          </p>
<pre class="programlisting">void wait(
    asio::error_code &amp; ec);
</pre>
<p>
            This function is used to wait for the timer to expire. This function
            blocks and does not return until the timer has expired.
          </p>
<h6>
<a name="asio.reference.basic_deadline_timer.wait.overload2.h0"></a>
            <span><a name="asio.reference.basic_deadline_timer.wait.overload2.parameters"></a></span><a class="link" href="overload2.html#asio.reference.basic_deadline_timer.wait.overload2.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">ec</span></dt>
<dd><p>
                  Set to indicate what error occurred, if any.
                </p></dd>
</dl>
</div>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../wait.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="../_basic_deadline_timer.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
