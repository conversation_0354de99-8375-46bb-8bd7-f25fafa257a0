<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_deadline_timer::cancel (2 of 2 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../cancel.html" title="basic_deadline_timer::cancel">
<link rel="prev" href="overload1.html" title="basic_deadline_timer::cancel (1 of 2 overloads)">
<link rel="next" href="../cancel_one.html" title="basic_deadline_timer::cancel_one">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../cancel.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="../cancel_one.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_deadline_timer.cancel.overload2"></a><a class="link" href="overload2.html" title="basic_deadline_timer::cancel (2 of 2 overloads)">basic_deadline_timer::cancel
          (2 of 2 overloads)</a>
</h5></div></div></div>
<p>
            Cancel any asynchronous operations that are waiting on the timer.
          </p>
<pre class="programlisting">std::size_t cancel(
    asio::error_code &amp; ec);
</pre>
<p>
            This function forces the completion of any pending asynchronous wait
            operations against the timer. The handler for each cancelled operation
            will be invoked with the <code class="computeroutput">asio::error::operation_aborted</code>
            error code.
          </p>
<p>
            Cancelling the timer does not change the expiry time.
          </p>
<h6>
<a name="asio.reference.basic_deadline_timer.cancel.overload2.h0"></a>
            <span><a name="asio.reference.basic_deadline_timer.cancel.overload2.parameters"></a></span><a class="link" href="overload2.html#asio.reference.basic_deadline_timer.cancel.overload2.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">ec</span></dt>
<dd><p>
                  Set to indicate what error occurred, if any.
                </p></dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_deadline_timer.cancel.overload2.h1"></a>
            <span><a name="asio.reference.basic_deadline_timer.cancel.overload2.return_value"></a></span><a class="link" href="overload2.html#asio.reference.basic_deadline_timer.cancel.overload2.return_value">Return
            Value</a>
          </h6>
<p>
            The number of asynchronous operations that were cancelled.
          </p>
<h6>
<a name="asio.reference.basic_deadline_timer.cancel.overload2.h2"></a>
            <span><a name="asio.reference.basic_deadline_timer.cancel.overload2.remarks"></a></span><a class="link" href="overload2.html#asio.reference.basic_deadline_timer.cancel.overload2.remarks">Remarks</a>
          </h6>
<p>
            If the timer has already expired when <code class="computeroutput">cancel()</code> is called,
            then the handlers for asynchronous wait operations will:
          </p>
<div class="itemizedlist"><ul class="itemizedlist" type="disc">
<li class="listitem">
                have already been invoked; or
              </li>
<li class="listitem">
                have been queued for invocation in the near future.
              </li>
</ul></div>
<p>
            These handlers can no longer be cancelled, and therefore are passed an
            error code that indicates the successful completion of the wait operation.
          </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../cancel.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="../cancel_one.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
