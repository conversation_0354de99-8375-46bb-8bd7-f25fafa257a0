// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cartographer.transform.proto;

// All coordinates are expressed in the right-handed Cartesian coordinate system
// used by <PERSON>to<PERSON> (x forward, y left, z up). Message names are chosen to
// mirror those used in the Eigen library.

message Vector2d {
  double x = 1;
  double y = 2;
}

message Vector2f {
  float x = 1;
  float y = 2;
}

message Vector3d {
  double x = 1;
  double y = 2;
  double z = 3;
}

message Vector3f {
  float x = 1;
  float y = 2;
  float z = 3;
}

message Vector4f {
  float x = 1;
  float y = 2;
  float z = 3;
  float t = 4;
}

message Quaterniond {
  double x = 1;
  double y = 2;
  double z = 3;
  double w = 4;
}

message Quaternionf {
  float x = 1;
  float y = 2;
  float z = 3;
  float w = 4;
}

message Rigid2d {
  Vector2d translation = 1;
  double rotation = 2;
}

message Rigid2f {
  Vector2f translation = 1;
  float rotation = 2;
}

message Rigid3d {
  Vector3d translation = 1;
  Quaterniond rotation = 2;
}

message Rigid3f {
  Vector3f translation = 1;
  Quaternionf rotation = 2;
}
