<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_datagram_socket::wait_type</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_datagram_socket.html" title="basic_datagram_socket">
<link rel="prev" href="wait/overload2.html" title="basic_datagram_socket::wait (2 of 2 overloads)">
<link rel="next" href="_basic_datagram_socket.html" title="basic_datagram_socket::~basic_datagram_socket">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="wait/overload2.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="_basic_datagram_socket.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_datagram_socket.wait_type"></a><a class="link" href="wait_type.html" title="basic_datagram_socket::wait_type">basic_datagram_socket::wait_type</a>
</h4></div></div></div>
<p>
          <span class="emphasis"><em>Inherited from socket_base.</em></span>
        </p>
<p>
          <a class="indexterm" name="asio.indexterm.basic_datagram_socket.wait_type"></a> 
Wait
          types.
        </p>
<pre class="programlisting">enum wait_type
</pre>
<p>
          <a class="indexterm" name="asio.indexterm.basic_datagram_socket.wait_type.wait_read"></a>
 <a class="indexterm" name="asio.indexterm.basic_datagram_socket.wait_type.wait_write"></a>
 <a class="indexterm" name="asio.indexterm.basic_datagram_socket.wait_type.wait_error"></a>
        </p>
<h6>
<a name="asio.reference.basic_datagram_socket.wait_type.h0"></a>
          <span><a name="asio.reference.basic_datagram_socket.wait_type.values"></a></span><a class="link" href="wait_type.html#asio.reference.basic_datagram_socket.wait_type.values">Values</a>
        </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">wait_read</span></dt>
<dd><p>
                Wait for a socket to become ready to read.
              </p></dd>
<dt><span class="term">wait_write</span></dt>
<dd><p>
                Wait for a socket to become ready to write.
              </p></dd>
<dt><span class="term">wait_error</span></dt>
<dd><p>
                Wait for a socket to have error conditions pending.
              </p></dd>
</dl>
</div>
<p>
          For use with <code class="computeroutput">basic_socket::wait()</code> and <code class="computeroutput">basic_socket::async_wait()</code>.
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="wait/overload2.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="_basic_datagram_socket.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
