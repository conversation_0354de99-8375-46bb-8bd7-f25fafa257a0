<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_datagram_socket::non_blocking (2 of 3 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../non_blocking.html" title="basic_datagram_socket::non_blocking">
<link rel="prev" href="overload1.html" title="basic_datagram_socket::non_blocking (1 of 3 overloads)">
<link rel="next" href="overload3.html" title="basic_datagram_socket::non_blocking (3 of 3 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../non_blocking.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload3.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_datagram_socket.non_blocking.overload2"></a><a class="link" href="overload2.html" title="basic_datagram_socket::non_blocking (2 of 3 overloads)">basic_datagram_socket::non_blocking
          (2 of 3 overloads)</a>
</h5></div></div></div>
<p>
            <span class="emphasis"><em>Inherited from basic_socket.</em></span>
          </p>
<p>
            Sets the non-blocking mode of the socket.
          </p>
<pre class="programlisting">void non_blocking(
    bool mode);
</pre>
<h6>
<a name="asio.reference.basic_datagram_socket.non_blocking.overload2.h0"></a>
            <span><a name="asio.reference.basic_datagram_socket.non_blocking.overload2.parameters"></a></span><a class="link" href="overload2.html#asio.reference.basic_datagram_socket.non_blocking.overload2.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">mode</span></dt>
<dd><p>
                  If <code class="computeroutput">true</code>, the socket's synchronous operations will
                  fail with <code class="computeroutput">asio::error::would_block</code> if they are unable
                  to perform the requested operation immediately. If <code class="computeroutput">false</code>,
                  synchronous operations will block until complete.
                </p></dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_datagram_socket.non_blocking.overload2.h1"></a>
            <span><a name="asio.reference.basic_datagram_socket.non_blocking.overload2.exceptions"></a></span><a class="link" href="overload2.html#asio.reference.basic_datagram_socket.non_blocking.overload2.exceptions">Exceptions</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">asio::system_error</span></dt>
<dd><p>
                  Thrown on failure.
                </p></dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_datagram_socket.non_blocking.overload2.h2"></a>
            <span><a name="asio.reference.basic_datagram_socket.non_blocking.overload2.remarks"></a></span><a class="link" href="overload2.html#asio.reference.basic_datagram_socket.non_blocking.overload2.remarks">Remarks</a>
          </h6>
<p>
            The non-blocking mode has no effect on the behaviour of asynchronous
            operations. Asynchronous operations will never fail with the error <code class="computeroutput">asio::error::would_block</code>.
          </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload1.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../non_blocking.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload3.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
