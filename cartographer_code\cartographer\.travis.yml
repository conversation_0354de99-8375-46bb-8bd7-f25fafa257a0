# Copyright 2016 The Cartographer Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

sudo: required
services: docker

# Cache intermediate Docker layers. For a description of how this works, see:
# https://giorgos.sealabs.net/docker-cache-on-travis-and-docker-112.html
cache:
  directories:
    - /home/<USER>/docker/

env:
  - LSB_RELEASE=bionic DOCKER_CACHE_FILE=/home/<USER>/docker/bionic-cache.tar.gz CC=gcc CXX=g++
  - LSB_RELEASE=focal DOCKER_CACHE_FILE=/home/<USER>/docker/focal-cache.tar.gz CC=gcc CXX=g++
  - LSB_RELEASE=stretch DOCKER_CACHE_FILE=/home/<USER>/docker/stretch-cache.tar.gz CC=gcc CXX=g++
  - LSB_RELEASE=buster DOCKER_CACHE_FILE=/home/<USER>/docker/buster-cache.tar.gz CC=gcc CXX=g++

before_install: scripts/load_docker_cache.sh

install: true
script:
  - docker build ${TRAVIS_BUILD_DIR} -t cartographer:${LSB_RELEASE} -f Dockerfile.${LSB_RELEASE}
      --build-arg cc=$CC --build-arg cxx=$CXX
  - scripts/save_docker_cache.sh
