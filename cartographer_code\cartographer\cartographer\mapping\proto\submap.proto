// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cartographer.mapping.proto;

import "cartographer/mapping/proto/grid_2d.proto";
import "cartographer/mapping/proto/hybrid_grid.proto";
import "cartographer/transform/proto/transform.proto";

// Serialized state of a Submap2D.
message Submap2D {
  transform.proto.Rigid3d local_pose = 1;
  int32 num_range_data = 2;
  bool finished = 3;
  Grid2D grid = 4;
}

// Serialized state of a Submap3D.
message Submap3D {
  transform.proto.Rigid3d local_pose = 1;
  int32 num_range_data = 2;
  bool finished = 3;
  HybridGrid high_resolution_hybrid_grid = 4;
  HybridGrid low_resolution_hybrid_grid = 5;
  repeated float rotational_scan_matcher_histogram = 6;
}
