<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_datagram_socket::async_wait</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_datagram_socket.html" title="basic_datagram_socket">
<link rel="prev" href="async_send_to/overload2.html" title="basic_datagram_socket::async_send_to (2 of 2 overloads)">
<link rel="next" href="at_mark.html" title="basic_datagram_socket::at_mark">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="async_send_to/overload2.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="at_mark.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_datagram_socket.async_wait"></a><a class="link" href="async_wait.html" title="basic_datagram_socket::async_wait">basic_datagram_socket::async_wait</a>
</h4></div></div></div>
<p>
          <span class="emphasis"><em>Inherited from basic_socket.</em></span>
        </p>
<p>
          <a class="indexterm" name="asio.indexterm.basic_datagram_socket.async_wait"></a> 
Asynchronously
          wait for the socket to become ready to read, ready to write, or to have
          pending error conditions.
        </p>
<pre class="programlisting">template&lt;
    typename <a class="link" href="../WaitHandler.html" title="Wait handler requirements">WaitHandler</a> = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>&gt;
<a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.automatic_deduction_of_initiating_function_return_type"><span class="emphasis"><em>DEDUCED</em></span></a> async_wait(
    wait_type w,
    WaitHandler &amp;&amp; handler = <a class="link" href="../asynchronous_operations.html#asio.reference.asynchronous_operations.default_completion_tokens"><span class="emphasis"><em>DEFAULT</em></span></a>);
</pre>
<p>
          This function is used to perform an asynchronous wait for a socket to enter
          a ready to read, write or error condition state.
        </p>
<h6>
<a name="asio.reference.basic_datagram_socket.async_wait.h0"></a>
          <span><a name="asio.reference.basic_datagram_socket.async_wait.parameters"></a></span><a class="link" href="async_wait.html#asio.reference.basic_datagram_socket.async_wait.parameters">Parameters</a>
        </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">w</span></dt>
<dd><p>
                Specifies the desired socket state.
              </p></dd>
<dt><span class="term">handler</span></dt>
<dd>
<p>
                The handler to be called when the wait operation completes. Copies
                will be made of the handler as required. The function signature of
                the handler must be:
</p>
<pre class="programlisting">void handler(
  const asio::error_code&amp; error // Result of operation
);
</pre>
<p>
                Regardless of whether the asynchronous operation completes immediately
                or not, the handler will not be invoked from within this function.
                On immediate completion, invocation of the handler will be performed
                in a manner equivalent to using <a class="link" href="../post.html" title="post"><code class="computeroutput">post</code></a>.
              </p>
</dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_datagram_socket.async_wait.h1"></a>
          <span><a name="asio.reference.basic_datagram_socket.async_wait.example"></a></span><a class="link" href="async_wait.html#asio.reference.basic_datagram_socket.async_wait.example">Example</a>
        </h6>
<pre class="programlisting">void wait_handler(const asio::error_code&amp; error)
{
  if (!error)
  {
    // Wait succeeded.
  }
}

...

asio::ip::tcp::socket socket(my_context);
...
socket.async_wait(asio::ip::tcp::socket::wait_read, wait_handler);
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="async_send_to/overload2.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="at_mark.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
