<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_datagram_socket::open (1 of 2 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../open.html" title="basic_datagram_socket::open">
<link rel="prev" href="../open.html" title="basic_datagram_socket::open">
<link rel="next" href="overload2.html" title="basic_datagram_socket::open (2 of 2 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../open.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../open.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_datagram_socket.open.overload1"></a><a class="link" href="overload1.html" title="basic_datagram_socket::open (1 of 2 overloads)">basic_datagram_socket::open
          (1 of 2 overloads)</a>
</h5></div></div></div>
<p>
            <span class="emphasis"><em>Inherited from basic_socket.</em></span>
          </p>
<p>
            Open the socket using the specified protocol.
          </p>
<pre class="programlisting">void open(
    const protocol_type &amp; protocol = protocol_type());
</pre>
<p>
            This function opens the socket so that it will use the specified protocol.
          </p>
<h6>
<a name="asio.reference.basic_datagram_socket.open.overload1.h0"></a>
            <span><a name="asio.reference.basic_datagram_socket.open.overload1.parameters"></a></span><a class="link" href="overload1.html#asio.reference.basic_datagram_socket.open.overload1.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">protocol</span></dt>
<dd><p>
                  An object specifying protocol parameters to be used.
                </p></dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_datagram_socket.open.overload1.h1"></a>
            <span><a name="asio.reference.basic_datagram_socket.open.overload1.exceptions"></a></span><a class="link" href="overload1.html#asio.reference.basic_datagram_socket.open.overload1.exceptions">Exceptions</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">asio::system_error</span></dt>
<dd><p>
                  Thrown on failure.
                </p></dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_datagram_socket.open.overload1.h2"></a>
            <span><a name="asio.reference.basic_datagram_socket.open.overload1.example"></a></span><a class="link" href="overload1.html#asio.reference.basic_datagram_socket.open.overload1.example">Example</a>
          </h6>
<pre class="programlisting">asio::ip::tcp::socket socket(my_context);
socket.open(asio::ip::tcp::v4());
</pre>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../open.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../open.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
