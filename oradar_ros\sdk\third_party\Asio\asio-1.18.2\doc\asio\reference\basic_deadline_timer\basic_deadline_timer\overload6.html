<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_deadline_timer::basic_deadline_timer (6 of 7 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../basic_deadline_timer.html" title="basic_deadline_timer::basic_deadline_timer">
<link rel="prev" href="overload5.html" title="basic_deadline_timer::basic_deadline_timer (5 of 7 overloads)">
<link rel="next" href="overload7.html" title="basic_deadline_timer::basic_deadline_timer (7 of 7 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload5.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_deadline_timer.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload7.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_deadline_timer.basic_deadline_timer.overload6"></a><a class="link" href="overload6.html" title="basic_deadline_timer::basic_deadline_timer (6 of 7 overloads)">basic_deadline_timer::basic_deadline_timer
          (6 of 7 overloads)</a>
</h5></div></div></div>
<p>
            Constructor to set a particular expiry time relative to now.
          </p>
<pre class="programlisting">template&lt;
    typename ExecutionContext&gt;
basic_deadline_timer(
    ExecutionContext &amp; context,
    const duration_type &amp; expiry_time,
    typename constraint&lt; is_convertible&lt; ExecutionContext &amp;, execution_context &amp; &gt;::value &gt;::type  = 0);
</pre>
<p>
            This constructor creates a timer and sets the expiry time.
          </p>
<h6>
<a name="asio.reference.basic_deadline_timer.basic_deadline_timer.overload6.h0"></a>
            <span><a name="asio.reference.basic_deadline_timer.basic_deadline_timer.overload6.parameters"></a></span><a class="link" href="overload6.html#asio.reference.basic_deadline_timer.basic_deadline_timer.overload6.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">context</span></dt>
<dd><p>
                  An execution context which provides the I/O executor that the timer
                  will use, by default, to dispatch handlers for any asynchronous
                  operations performed on the timer.
                </p></dd>
<dt><span class="term">expiry_time</span></dt>
<dd><p>
                  The expiry time to be used for the timer, relative to now.
                </p></dd>
</dl>
</div>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="overload5.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_deadline_timer.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload7.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
