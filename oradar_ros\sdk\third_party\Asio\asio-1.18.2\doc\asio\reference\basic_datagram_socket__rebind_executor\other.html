<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_datagram_socket::rebind_executor::other</title>
<link rel="stylesheet" href="../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../index.html" title="Asio">
<link rel="up" href="../basic_datagram_socket__rebind_executor.html" title="basic_datagram_socket::rebind_executor">
<link rel="prev" href="../basic_datagram_socket__rebind_executor.html" title="basic_datagram_socket::rebind_executor">
<link rel="next" href="../basic_deadline_timer.html" title="basic_deadline_timer">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../basic_datagram_socket__rebind_executor.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket__rebind_executor.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../basic_deadline_timer.html"><img src="../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="asio.reference.basic_datagram_socket__rebind_executor.other"></a><a class="link" href="other.html" title="basic_datagram_socket::rebind_executor::other">basic_datagram_socket::rebind_executor::other</a>
</h4></div></div></div>
<p>
          <a class="indexterm" name="asio.indexterm.basic_datagram_socket__rebind_executor.other"></a> 
The
          socket type when rebound to the specified executor.
        </p>
<pre class="programlisting">typedef basic_datagram_socket&lt; Protocol, Executor1 &gt; other;
</pre>
<h6>
<a name="asio.reference.basic_datagram_socket__rebind_executor.other.h0"></a>
          <span><a name="asio.reference.basic_datagram_socket__rebind_executor.other.types"></a></span><a class="link" href="other.html#asio.reference.basic_datagram_socket__rebind_executor.other.types">Types</a>
        </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Name
                  </p>
                </th>
<th>
                  <p>
                    Description
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket__rebind_executor.html" title="basic_datagram_socket::rebind_executor"><span class="bold"><strong>rebind_executor</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Rebinds the socket type to another executor.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/broadcast.html" title="basic_datagram_socket::broadcast"><span class="bold"><strong>broadcast</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Socket option to permit sending of broadcast messages.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/bytes_readable.html" title="basic_datagram_socket::bytes_readable"><span class="bold"><strong>bytes_readable</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    IO control command to get the amount of data that can be read
                    without blocking.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/debug.html" title="basic_datagram_socket::debug"><span class="bold"><strong>debug</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Socket option to enable socket-level debugging.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/do_not_route.html" title="basic_datagram_socket::do_not_route"><span class="bold"><strong>do_not_route</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Socket option to prevent routing, use local interfaces only.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/enable_connection_aborted.html" title="basic_datagram_socket::enable_connection_aborted"><span class="bold"><strong>enable_connection_aborted</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Socket option to report aborted connections on accept.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/endpoint_type.html" title="basic_datagram_socket::endpoint_type"><span class="bold"><strong>endpoint_type</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    The endpoint type.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/executor_type.html" title="basic_datagram_socket::executor_type"><span class="bold"><strong>executor_type</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    The type of the executor associated with the object.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/keep_alive.html" title="basic_datagram_socket::keep_alive"><span class="bold"><strong>keep_alive</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Socket option to send keep-alives.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/linger.html" title="basic_datagram_socket::linger"><span class="bold"><strong>linger</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Socket option to specify whether the socket lingers on close
                    if unsent data is present.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/lowest_layer_type.html" title="basic_datagram_socket::lowest_layer_type"><span class="bold"><strong>lowest_layer_type</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    A basic_socket is always the lowest layer.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/message_flags.html" title="basic_datagram_socket::message_flags"><span class="bold"><strong>message_flags</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Bitmask type for flags that can be passed to send and receive
                    operations.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/native_handle_type.html" title="basic_datagram_socket::native_handle_type"><span class="bold"><strong>native_handle_type</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    The native representation of a socket.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/out_of_band_inline.html" title="basic_datagram_socket::out_of_band_inline"><span class="bold"><strong>out_of_band_inline</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Socket option for putting received out-of-band data inline.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/protocol_type.html" title="basic_datagram_socket::protocol_type"><span class="bold"><strong>protocol_type</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    The protocol type.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/receive_buffer_size.html" title="basic_datagram_socket::receive_buffer_size"><span class="bold"><strong>receive_buffer_size</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Socket option for the receive buffer size of a socket.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/receive_low_watermark.html" title="basic_datagram_socket::receive_low_watermark"><span class="bold"><strong>receive_low_watermark</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Socket option for the receive low watermark.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/reuse_address.html" title="basic_datagram_socket::reuse_address"><span class="bold"><strong>reuse_address</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Socket option to allow the socket to be bound to an address that
                    is already in use.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/send_buffer_size.html" title="basic_datagram_socket::send_buffer_size"><span class="bold"><strong>send_buffer_size</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Socket option for the send buffer size of a socket.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/send_low_watermark.html" title="basic_datagram_socket::send_low_watermark"><span class="bold"><strong>send_low_watermark</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Socket option for the send low watermark.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/shutdown_type.html" title="basic_datagram_socket::shutdown_type"><span class="bold"><strong>shutdown_type</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Different ways a socket may be shutdown.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/wait_type.html" title="basic_datagram_socket::wait_type"><span class="bold"><strong>wait_type</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Wait types.
                  </p>
                </td>
</tr>
</tbody>
</table></div>
<h6>
<a name="asio.reference.basic_datagram_socket__rebind_executor.other.h1"></a>
          <span><a name="asio.reference.basic_datagram_socket__rebind_executor.other.member_functions"></a></span><a class="link" href="other.html#asio.reference.basic_datagram_socket__rebind_executor.other.member_functions">Member
          Functions</a>
        </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Name
                  </p>
                </th>
<th>
                  <p>
                    Description
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/assign.html" title="basic_datagram_socket::assign"><span class="bold"><strong>assign</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Assign an existing native socket to the socket.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/async_connect.html" title="basic_datagram_socket::async_connect"><span class="bold"><strong>async_connect</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Start an asynchronous connect.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/async_receive.html" title="basic_datagram_socket::async_receive"><span class="bold"><strong>async_receive</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Start an asynchronous receive on a connected socket.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/async_receive_from.html" title="basic_datagram_socket::async_receive_from"><span class="bold"><strong>async_receive_from</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Start an asynchronous receive.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/async_send.html" title="basic_datagram_socket::async_send"><span class="bold"><strong>async_send</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Start an asynchronous send on a connected socket.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/async_send_to.html" title="basic_datagram_socket::async_send_to"><span class="bold"><strong>async_send_to</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Start an asynchronous send.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/async_wait.html" title="basic_datagram_socket::async_wait"><span class="bold"><strong>async_wait</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Asynchronously wait for the socket to become ready to read, ready
                    to write, or to have pending error conditions.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/at_mark.html" title="basic_datagram_socket::at_mark"><span class="bold"><strong>at_mark</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Determine whether the socket is at the out-of-band data mark.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/available.html" title="basic_datagram_socket::available"><span class="bold"><strong>available</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Determine the number of bytes available for reading.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/basic_datagram_socket.html" title="basic_datagram_socket::basic_datagram_socket"><span class="bold"><strong>basic_datagram_socket</strong></span></a> <span class="silver">[constructor]</span>
                  </p>
                </td>
<td>
                  <p>
                    Construct a basic_datagram_socket without opening it. <br>
                    <span class="silver"> —</span><br> Construct and open a basic_datagram_socket. <br> <span class="silver"> —</span><br>
                    Construct a basic_datagram_socket, opening it and binding it
                    to the given local endpoint. <br> <span class="silver"> —</span><br> Construct a basic_datagram_socket
                    on an existing native socket. <br> <span class="silver"> —</span><br> Move-construct a
                    basic_datagram_socket from another. <br> <span class="silver"> —</span><br> Move-construct
                    a basic_datagram_socket from a socket of another protocol type.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/bind.html" title="basic_datagram_socket::bind"><span class="bold"><strong>bind</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Bind the socket to the given local endpoint.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/cancel.html" title="basic_datagram_socket::cancel"><span class="bold"><strong>cancel</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Cancel all asynchronous operations associated with the socket.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/close.html" title="basic_datagram_socket::close"><span class="bold"><strong>close</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Close the socket.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/connect.html" title="basic_datagram_socket::connect"><span class="bold"><strong>connect</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Connect the socket to the specified endpoint.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/get_executor.html" title="basic_datagram_socket::get_executor"><span class="bold"><strong>get_executor</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Get the executor associated with the object.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/get_option.html" title="basic_datagram_socket::get_option"><span class="bold"><strong>get_option</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Get an option from the socket.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/io_control.html" title="basic_datagram_socket::io_control"><span class="bold"><strong>io_control</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Perform an IO control command on the socket.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/is_open.html" title="basic_datagram_socket::is_open"><span class="bold"><strong>is_open</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Determine whether the socket is open.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/local_endpoint.html" title="basic_datagram_socket::local_endpoint"><span class="bold"><strong>local_endpoint</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Get the local endpoint of the socket.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/lowest_layer.html" title="basic_datagram_socket::lowest_layer"><span class="bold"><strong>lowest_layer</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Get a reference to the lowest layer. <br> <span class="silver"> —</span><br> Get a const
                    reference to the lowest layer.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/native_handle.html" title="basic_datagram_socket::native_handle"><span class="bold"><strong>native_handle</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Get the native socket representation.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/native_non_blocking.html" title="basic_datagram_socket::native_non_blocking"><span class="bold"><strong>native_non_blocking</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Gets the non-blocking mode of the native socket implementation.
                    <br> <span class="silver"> —</span><br> Sets the non-blocking mode of the native socket
                    implementation.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/non_blocking.html" title="basic_datagram_socket::non_blocking"><span class="bold"><strong>non_blocking</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Gets the non-blocking mode of the socket. <br> <span class="silver"> —</span><br> Sets
                    the non-blocking mode of the socket.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/open.html" title="basic_datagram_socket::open"><span class="bold"><strong>open</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Open the socket using the specified protocol.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/operator_eq_.html" title="basic_datagram_socket::operator="><span class="bold"><strong>operator=</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Move-assign a basic_datagram_socket from another. <br> <span class="silver"> —</span><br>
                    Move-assign a basic_datagram_socket from a socket of another
                    protocol type.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/receive.html" title="basic_datagram_socket::receive"><span class="bold"><strong>receive</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Receive some data on a connected socket.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/receive_from.html" title="basic_datagram_socket::receive_from"><span class="bold"><strong>receive_from</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Receive a datagram with the endpoint of the sender.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/release.html" title="basic_datagram_socket::release"><span class="bold"><strong>release</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Release ownership of the underlying native socket.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/remote_endpoint.html" title="basic_datagram_socket::remote_endpoint"><span class="bold"><strong>remote_endpoint</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Get the remote endpoint of the socket.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/send.html" title="basic_datagram_socket::send"><span class="bold"><strong>send</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Send some data on a connected socket.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/send_to.html" title="basic_datagram_socket::send_to"><span class="bold"><strong>send_to</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Send a datagram to the specified endpoint.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/set_option.html" title="basic_datagram_socket::set_option"><span class="bold"><strong>set_option</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Set an option on the socket.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/shutdown.html" title="basic_datagram_socket::shutdown"><span class="bold"><strong>shutdown</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Disable sends or receives on the socket.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/wait.html" title="basic_datagram_socket::wait"><span class="bold"><strong>wait</strong></span></a>
                  </p>
                </td>
<td>
                  <p>
                    Wait for the socket to become ready to read, ready to write,
                    or to have pending error conditions.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/_basic_datagram_socket.html" title="basic_datagram_socket::~basic_datagram_socket"><span class="bold"><strong>~basic_datagram_socket</strong></span></a> <span class="silver">[destructor]</span>
                  </p>
                </td>
<td>
                  <p>
                    Destroys the socket.
                  </p>
                </td>
</tr>
</tbody>
</table></div>
<h6>
<a name="asio.reference.basic_datagram_socket__rebind_executor.other.h2"></a>
          <span><a name="asio.reference.basic_datagram_socket__rebind_executor.other.data_members"></a></span><a class="link" href="other.html#asio.reference.basic_datagram_socket__rebind_executor.other.data_members">Data
          Members</a>
        </h6>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
</colgroup>
<thead><tr>
<th>
                  <p>
                    Name
                  </p>
                </th>
<th>
                  <p>
                    Description
                  </p>
                </th>
</tr></thead>
<tbody>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/max_connections.html" title="basic_datagram_socket::max_connections"><span class="bold"><strong>max_connections</strong></span></a> <span class="silver">[static]</span>
                  </p>
                </td>
<td>
                  <p>
                    (Deprecated: Use max_listen_connections.) The maximum length
                    of the queue of pending incoming connections.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/max_listen_connections.html" title="basic_datagram_socket::max_listen_connections"><span class="bold"><strong>max_listen_connections</strong></span></a> <span class="silver">[static]</span>
                  </p>
                </td>
<td>
                  <p>
                    The maximum length of the queue of pending incoming connections.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/message_do_not_route.html" title="basic_datagram_socket::message_do_not_route"><span class="bold"><strong>message_do_not_route</strong></span></a> <span class="silver">[static]</span>
                  </p>
                </td>
<td>
                  <p>
                    Specify that the data should not be subject to routing.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/message_end_of_record.html" title="basic_datagram_socket::message_end_of_record"><span class="bold"><strong>message_end_of_record</strong></span></a> <span class="silver">[static]</span>
                  </p>
                </td>
<td>
                  <p>
                    Specifies that the data marks the end of a record.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/message_out_of_band.html" title="basic_datagram_socket::message_out_of_band"><span class="bold"><strong>message_out_of_band</strong></span></a> <span class="silver">[static]</span>
                  </p>
                </td>
<td>
                  <p>
                    Process out-of-band data.
                  </p>
                </td>
</tr>
<tr>
<td>
                  <p>
                    <a class="link" href="../basic_datagram_socket/message_peek.html" title="basic_datagram_socket::message_peek"><span class="bold"><strong>message_peek</strong></span></a> <span class="silver">[static]</span>
                  </p>
                </td>
<td>
                  <p>
                    Peek at incoming data without removing it from the input queue.
                  </p>
                </td>
</tr>
</tbody>
</table></div>
<p>
          The <a class="link" href="../basic_datagram_socket.html" title="basic_datagram_socket"><code class="computeroutput">basic_datagram_socket</code></a>
          class template provides asynchronous and blocking datagram-oriented socket
          functionality.
        </p>
<h6>
<a name="asio.reference.basic_datagram_socket__rebind_executor.other.h3"></a>
          <span><a name="asio.reference.basic_datagram_socket__rebind_executor.other.thread_safety"></a></span><a class="link" href="other.html#asio.reference.basic_datagram_socket__rebind_executor.other.thread_safety">Thread
          Safety</a>
        </h6>
<p>
          <span class="emphasis"><em>Distinct</em></span> <span class="emphasis"><em>objects:</em></span> Safe.
        </p>
<p>
          <span class="emphasis"><em>Shared</em></span> <span class="emphasis"><em>objects:</em></span> Unsafe.
        </p>
<p>
          Synchronous <code class="computeroutput">send</code>, <code class="computeroutput">send_to</code>, <code class="computeroutput">receive</code>,
          <code class="computeroutput">receive_from</code>, and <code class="computeroutput">connect</code> operations are thread
          safe with respect to each other, if the underlying operating system calls
          are also thread safe. This means that it is permitted to perform concurrent
          calls to these synchronous operations on a single socket object. Other
          synchronous operations, such as <code class="computeroutput">open</code> or <code class="computeroutput">close</code>,
          are not thread safe.
        </p>
<h6>
<a name="asio.reference.basic_datagram_socket__rebind_executor.other.h4"></a>
          <span><a name="asio.reference.basic_datagram_socket__rebind_executor.other.requirements"></a></span><a class="link" href="other.html#asio.reference.basic_datagram_socket__rebind_executor.other.requirements">Requirements</a>
        </h6>
<p>
          <span class="emphasis"><em>Header: </em></span><code class="literal">asio/basic_datagram_socket.hpp</code>
        </p>
<p>
          <span class="emphasis"><em>Convenience header: </em></span><code class="literal">asio.hpp</code>
        </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../basic_datagram_socket__rebind_executor.html"><img src="../../../prev.png" alt="Prev"></a><a accesskey="u" href="../basic_datagram_socket__rebind_executor.html"><img src="../../../up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../home.png" alt="Home"></a><a accesskey="n" href="../basic_deadline_timer.html"><img src="../../../next.png" alt="Next"></a>
</div>
</body>
</html>
