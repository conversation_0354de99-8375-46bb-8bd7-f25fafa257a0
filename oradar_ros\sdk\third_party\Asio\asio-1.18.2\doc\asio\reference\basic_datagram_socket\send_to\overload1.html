<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>basic_datagram_socket::send_to (1 of 3 overloads)</title>
<link rel="stylesheet" href="../../../../boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.75.2">
<link rel="home" href="../../../../index.html" title="Asio">
<link rel="up" href="../send_to.html" title="basic_datagram_socket::send_to">
<link rel="prev" href="../send_to.html" title="basic_datagram_socket::send_to">
<link rel="next" href="overload2.html" title="basic_datagram_socket::send_to (2 of 3 overloads)">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr><td valign="top"><img alt="asio C++ library" width="250" height="60" src="../../../../asio.png"></td></tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../send_to.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../send_to.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h5 class="title">
<a name="asio.reference.basic_datagram_socket.send_to.overload1"></a><a class="link" href="overload1.html" title="basic_datagram_socket::send_to (1 of 3 overloads)">basic_datagram_socket::send_to
          (1 of 3 overloads)</a>
</h5></div></div></div>
<p>
            Send a datagram to the specified endpoint.
          </p>
<pre class="programlisting">template&lt;
    typename <a class="link" href="../../ConstBufferSequence.html" title="Constant buffer sequence requirements">ConstBufferSequence</a>&gt;
std::size_t send_to(
    const ConstBufferSequence &amp; buffers,
    const endpoint_type &amp; destination);
</pre>
<p>
            This function is used to send a datagram to the specified remote endpoint.
            The function call will block until the data has been sent successfully
            or an error occurs.
          </p>
<h6>
<a name="asio.reference.basic_datagram_socket.send_to.overload1.h0"></a>
            <span><a name="asio.reference.basic_datagram_socket.send_to.overload1.parameters"></a></span><a class="link" href="overload1.html#asio.reference.basic_datagram_socket.send_to.overload1.parameters">Parameters</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">buffers</span></dt>
<dd><p>
                  One or more data buffers to be sent to the remote endpoint.
                </p></dd>
<dt><span class="term">destination</span></dt>
<dd><p>
                  The remote endpoint to which the data will be sent.
                </p></dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_datagram_socket.send_to.overload1.h1"></a>
            <span><a name="asio.reference.basic_datagram_socket.send_to.overload1.return_value"></a></span><a class="link" href="overload1.html#asio.reference.basic_datagram_socket.send_to.overload1.return_value">Return
            Value</a>
          </h6>
<p>
            The number of bytes sent.
          </p>
<h6>
<a name="asio.reference.basic_datagram_socket.send_to.overload1.h2"></a>
            <span><a name="asio.reference.basic_datagram_socket.send_to.overload1.exceptions"></a></span><a class="link" href="overload1.html#asio.reference.basic_datagram_socket.send_to.overload1.exceptions">Exceptions</a>
          </h6>
<div class="variablelist">
<p class="title"><b></b></p>
<dl>
<dt><span class="term">asio::system_error</span></dt>
<dd><p>
                  Thrown on failure.
                </p></dd>
</dl>
</div>
<h6>
<a name="asio.reference.basic_datagram_socket.send_to.overload1.h3"></a>
            <span><a name="asio.reference.basic_datagram_socket.send_to.overload1.example"></a></span><a class="link" href="overload1.html#asio.reference.basic_datagram_socket.send_to.overload1.example">Example</a>
          </h6>
<p>
            To send a single data buffer use the <a class="link" href="../../buffer.html" title="buffer"><code class="computeroutput">buffer</code></a>
            function as follows:
          </p>
<pre class="programlisting">asio::ip::udp::endpoint destination(
    asio::ip::address::from_string("*******"), 12345);
socket.send_to(asio::buffer(data, size), destination);
</pre>
<p>
            See the <a class="link" href="../../buffer.html" title="buffer"><code class="computeroutput">buffer</code></a>
            documentation for information on sending multiple buffers in one go,
            and how to use it with arrays, boost::array or std::vector.
          </p>
</div>
<table xmlns:rev="http://www.cs.rpi.edu/~gregod/boost/tools/doc/revision" width="100%"><tr>
<td align="left"></td>
<td align="right"><div class="copyright-footer">Copyright © 2003-2021 Christopher
      M. Kohlhoff<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../send_to.html"><img src="../../../../prev.png" alt="Prev"></a><a accesskey="u" href="../send_to.html"><img src="../../../../up.png" alt="Up"></a><a accesskey="h" href="../../../../index.html"><img src="../../../../home.png" alt="Home"></a><a accesskey="n" href="overload2.html"><img src="../../../../next.png" alt="Next"></a>
</div>
</body>
</html>
